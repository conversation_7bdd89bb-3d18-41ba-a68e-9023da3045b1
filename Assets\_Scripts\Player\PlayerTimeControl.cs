using System.Collections;
using System.Collections.Generic;
using FluffyUnderware.Curvy.Controllers;
using FMODUnity;
using MoreMountains.Feedbacks;
using UnityEngine;
using UnityEngine.VFX;
using BTR;
using BTR.Projectiles;
using Cysharp.Threading.Tasks;
using System;
using System.Threading;
using Stylo.Epoch;

namespace BTR
{
    public class PlayerTimeControl : MonoBehaviour
    {
        private CrosshairCore crosshairCore;
        private PlayerLocking playerLocking;
        private SplineController splineControl;
        private PlayerMovement pMove;
        private AudioManager audioManager;
        private EnemyManager enemyManager;
        private CancellationTokenSource _destroyToken;
    private const string LOG_TAG = "[PlayerTimeControl]";

        [Header("Component References")]
        [SerializeField] private MMF_Player rewindFeedback;
        [SerializeField] private MMF_Player longrewindFeedback;
        [SerializeField] private ParticleSystem temporalBlast;
        [SerializeField] private ParticleSystem RewindFXScan;
        [SerializeField] private VisualEffect RewindFX;
        [SerializeField] private VisualEffect slowTime;

        [Header("Time Control Settings")]
        [Tooltip("Time scale during rewind")]
        [SerializeField] private float rewindTimeScale = -2f;

        [Tooltip("Duration of the rewind effect")]
        [SerializeField] private float rewindDuration = 3f;

        [Tooltip("Duration to return to normal time")]
        [SerializeField] private float returnToNormalDuration = 0.25f;

        [Tooltip("Time scale during slow motion")]
        [SerializeField] private float slowTimeScale = 0.1f;

        [Tooltip("Duration of the slow motion effect")]
        [SerializeField] private float slowTimeDuration = 5f;

        [Tooltip("Cooldown between rewind actions")]
        [SerializeField] private float rewindCooldown = 0.5f;

        [Tooltip("Maximum duration for rewind")]
        [SerializeField] private float maxRewindDuration = 1f;

        [Header("JPG Effect Settings")]
        [Tooltip("JPG effect intensity during rewind")]
        [SerializeField] private float rewindJPGIntensity = 0.2f;

        [Tooltip("JPG effect intensity during slow motion")]
        [SerializeField] private float slowJPGIntensity = 0.4f;

        [Tooltip("Duration to apply JPG effect")]
        [SerializeField] private float jpgEffectDuration = 0.5f;

        [Header("Player Speed Control")]
        [Tooltip("Player speed multiplier during slow motion (0.5 = half speed)")]
        [SerializeField] private float playerSlowSpeedMultiplier = 0.5f;

        [Tooltip("Speed transition duration when entering/exiting slow motion")]
        [SerializeField] private float speedTransitionDuration = 0.25f;

        [Tooltip("Enable player speed control during slow motion")]
        [SerializeField] private bool enablePlayerSpeedControl = true;

        private float lastRewindTime = 0f;
        private bool delayLoop = false;
        private float originalSpeed;
        private float originalPlayerSpeed; // Initial speed stored at startup for reference
        private float currentNormalSpeed; // Current "normal" speed before time effects
        private float baselineJPGIntensity;
        private CancellationTokenSource _currentRewindCts;
        private Coroutine speedTransitionCoroutine;

        [Header("Epoch Integration")]
        [Tooltip("The key of the Epoch clock to control (default: Global)")]
        [SerializeField] private string epochClockKey = "Global";

        [Header("Debug Logging")]
        [SerializeField] private bool enableDebugLogs = true;
        // Use Debug.Log, Debug.LogWarning, Debug.LogError for debug output:
        // Example:
        // if (enableDebugLogs)
        //     Debug.Log($"[{GetType().Name}] Some debug message");

        private void Awake()
    {
        Debug.Log($"{LOG_TAG} Awake called. Initializing _destroyToken.");
        _destroyToken = new CancellationTokenSource();
        // Corrected: Removed extra opening brace here
            crosshairCore = GetComponent<CrosshairCore>();
            playerLocking = GetComponent<PlayerLocking>();
            splineControl = FindFirstObjectByType<SplineController>();
            pMove = FindFirstObjectByType<PlayerMovement>();
            audioManager = AudioManager.Instance;
            enemyManager = EnemyManager.Instance;

            // Store the baseline Flux intensity on startup
            if (FluxController.Instance != null)
            {
                baselineJPGIntensity = FluxController.Instance.GetCurrentIntensity();
            }

            // Store the original player speed from SplineController
            if (splineControl != null)
            {
                originalPlayerSpeed = splineControl.Speed;
                if (enableDebugLogs)
                {
                    Debug.Log($"{LOG_TAG} Stored original player speed: {originalPlayerSpeed}");
                }
            }
            else
            {
                Debug.LogWarning($"{LOG_TAG} SplineController not found - player speed control will be disabled");
            }

            if (crosshairCore == null || playerLocking == null || splineControl == null || pMove == null || audioManager == null || enemyManager == null)
            {
                if (enableDebugLogs)
                    Debug.LogError($"[{GetType().Name}] Required components not found on the same GameObject or in the scene.");
            }
        }

        private void OnDestroy()
    {
        Debug.Log($"{LOG_TAG} OnDestroy called. Current _destroyToken IsCancellationRequested: {_destroyToken?.IsCancellationRequested}");
        if (_destroyToken != null && !_destroyToken.IsCancellationRequested)
        {
            Debug.Log($"{LOG_TAG} OnDestroy: Actively cancelling _destroyToken.");
            _destroyToken.Cancel();
        }
        _destroyToken?.Dispose(); // Dispose if not null
        _destroyToken = null;

        if (_currentRewindCts != null)
        {
            Debug.Log($"{LOG_TAG} OnDestroy: Cancelling and disposing _currentRewindCts. IsCancellationRequested: {_currentRewindCts.IsCancellationRequested}");
            if (!_currentRewindCts.IsCancellationRequested) _currentRewindCts.Cancel();
            _currentRewindCts.Dispose();
            _currentRewindCts = null;
        }
        
        // Clean up speed transition coroutine
        if (speedTransitionCoroutine != null)
        {
            StopCoroutine(speedTransitionCoroutine);
            speedTransitionCoroutine = null;
        }
        
        Debug.Log($"{LOG_TAG} OnDestroy finished.");
    }

        public async UniTaskVoid HandleRewindToBeat()
        {
            if (!crosshairCore.CheckRewindToBeat() || Time.time - lastRewindTime <= rewindCooldown)
                return;

            try
            {
                float timeSinceLastLaunch = Time.time - crosshairCore.lastProjectileLaunchTime;
                Debug.Log($"Time since last projectile launch: {timeSinceLastLaunch}, QTE window: {CrosshairCore.QTE_TRIGGER_WINDOW}, QTE Locked targets: {playerLocking.qteEnemyLockList.Count}");

                lastRewindTime = Time.time;
                if (timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW && playerLocking.qteEnemyLockList.Count > 0)
                {
                    Debug.Log("QTE Initiated for Rewind");
                    await TriggerQTEAsync(rewindDuration, 3);
                }
                else
                {
                    Debug.Log($"Rewind started without QTE. Time condition met: {timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW}, Targets condition met: {playerLocking.qteEnemyLockList.Count > 0}");
                    await RewindToBeatAsync();
                }
            }
            catch (OperationCanceledException ex)
            {
                Debug.Log($"{LOG_TAG} Rewind operation cancelled: {ex.Message}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error during rewind: {e.Message}");
            }
        }

        public async UniTaskVoid HandleSlowToBeat()
        {
            if (!crosshairCore.CheckSlowToBeat())
                return;

            try
            {
                float timeSinceLastLaunch = Time.time - crosshairCore.lastProjectileLaunchTime;
                Debug.Log($"Time since last projectile launch: {timeSinceLastLaunch}, QTE window: {CrosshairCore.QTE_TRIGGER_WINDOW}, QTE Locked targets: {playerLocking.qteEnemyLockList.Count}");

                if (timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW && playerLocking.qteEnemyLockList.Count > 0)
                {
                    Debug.Log("QTE Initiated for Slow");
                    await TriggerQTEAsync(slowTimeDuration);
                }
                else
                {
                    Debug.Log($"Slow started without QTE. Time condition met: {timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW}, Targets condition met: {playerLocking.qteEnemyLockList.Count > 0}");
                    await SlowToBeatAsync();
                }
            }
            catch (OperationCanceledException ex)
            {
                Debug.Log($"{LOG_TAG} Slow operation cancelled: {ex.Message}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error during slow: {e.Message}");
            }
        }

        private async UniTask TriggerQTEAsync(float duration, int difficulty = 3)
        {
            if (QuickTimeEventManager.Instance == null)
            {
                Debug.LogError("QuickTimeEventManager instance is null");
                return;
            }

            try
            {
                var qteCompletionSource = new UniTaskCompletionSource<bool>();
                void HandleComplete(bool success)
                {
                    QuickTimeEventManager.Instance.OnQteComplete -= HandleComplete;
                    qteCompletionSource.TrySetResult(success);
                }

                QuickTimeEventManager.Instance.OnQteComplete += HandleComplete;
                QuickTimeEventManager.Instance.StartQTE(duration, difficulty);

                _currentRewindCts?.Cancel();
                _currentRewindCts?.Dispose();
                _currentRewindCts = CancellationTokenSource.CreateLinkedTokenSource(_destroyToken.Token);

                // Start rewind and wait for QTE completion
                _ = RewindToBeatAsync().AttachExternalCancellation(_currentRewindCts.Token);
                bool success = await qteCompletionSource.Task;

                if (success)
                {
                    _currentRewindCts.Cancel();
                    StopRewindEffect();
                    ApplyIncreasedDamage();
                }
                else
                {
                    playerLocking.ClearLockedTargets();
                }

                await ResetMusicStateAsync();
                Debug.Log($"QTE completed with success: {success}");
            }
            catch (OperationCanceledException ex)
            {
                Debug.Log($"{LOG_TAG} QTE operation cancelled: {ex.Message}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error during QTE: {e.Message}");
                throw;
            }
        }

        public async UniTask ResetMusicStateAsync()
    {
        Debug.Log($"{LOG_TAG} ResetMusicStateAsync START. Token: {_destroyToken?.Token.IsCancellationRequested}");
        // Corrected: Removed extra opening brace here
            try
            {
                if (audioManager != null)
                {
                    Debug.Log($"{LOG_TAG} ResetMusicStateAsync: Attempting to reset music state. Token: {_destroyToken?.Token.IsCancellationRequested}");
                    Debug.Log($"{LOG_TAG} ResetMusicStateAsync: Calling audioManager.SetMusicParameterAsync('Time State', 0f). Token: {_destroyToken?.Token.IsCancellationRequested}");
                    await audioManager.SetMusicParameterAsync("Time State", 0f, _destroyToken.Token);
                    Debug.Log($"{LOG_TAG} ResetMusicStateAsync: audioManager.SetMusicParameterAsync completed.");
                }
                else
                {
                    Debug.LogError("AudioManager.Instance is null in ResetMusicState");
                }
            }
            catch (OperationCanceledException ex)
            {
                Debug.Log($"{LOG_TAG} ResetMusicStateAsync operation cancelled: {ex.Message}");
            }
            catch (Exception e)
            {
                Debug.LogError($"{LOG_TAG} Error resetting music state: {e.Message}. Token IsCancellationRequested: {_destroyToken?.Token.IsCancellationRequested}", this.gameObject);
                throw;
            }
        Debug.Log($"{LOG_TAG} ResetMusicStateAsync END.");
    }

    private async UniTask RewindToBeatAsync()
    {
        Debug.Log($"{LOG_TAG} RewindToBeatAsync START. delayLoop: {delayLoop}, Token: {_destroyToken?.Token.IsCancellationRequested}");
        // Corrected: Removed extra opening brace here
            if (delayLoop)
                return;

            try
            {
                delayLoop = true;
            ActivateRewindEffects(true);
            enemyManager.ShowAllTargetIndicators();
            float startPosition = splineControl.RelativePosition;
            
            // Capture current speed before rewind effect
            currentNormalSpeed = splineControl.Speed;
            originalSpeed = currentNormalSpeed; // Keep for backward compatibility
            
            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} RewindToBeatAsync: Captured current speed before rewind: {currentNormalSpeed:F2}");
            }

                // Set new position
                float rewindSpeed = originalSpeed * 0.25f;
                float rewindDistance = rewindSpeed * rewindDuration;
                float newPosition = Mathf.Clamp01(startPosition - rewindDistance);

                // Apply rewind effects
                splineControl.Speed = -rewindSpeed;
                crosshairCore.TriggerRewindStart(rewindTimeScale);
                rewindFeedback.PlayFeedbacks();

                FluxController.Instance.ApplyRewindEffects();

                // Use Epoch to set the time scale for better audio integration
                Debug.Log($"{LOG_TAG} RewindToBeatAsync: Setting EpochClock '{epochClockKey}' to -1f.");
                SetEpochClockTimeScale(epochClockKey, -1f);
                if (audioManager != null)
                {
                    Debug.Log($"{LOG_TAG} RewindToBeatAsync: Calling audioManager.SetMusicParameterAsync('Time State', 1f). Token: {_destroyToken?.Token.IsCancellationRequested}");
                    await audioManager.SetMusicParameterAsync("Time State", 1f, _destroyToken.Token);
                    Debug.Log($"{LOG_TAG} RewindToBeatAsync: audioManager.SetMusicParameterAsync completed.");
                }

                Debug.Log($"{LOG_TAG} RewindToBeatAsync: Starting UniTask.Delay for {rewindDuration}s. Token: {_destroyToken?.Token.IsCancellationRequested}");
                await UniTask.Delay(TimeSpan.FromSeconds(rewindDuration), cancellationToken: _destroyToken.Token);
                Debug.Log($"{LOG_TAG} RewindToBeatAsync: UniTask.Delay completed.");

                // Reset time scale
                SetEpochClockTimeScale(epochClockKey, 1f);

                // Ensure the position is set correctly after the rewind
                splineControl.RelativePosition = newPosition;

                StopRewindEffect();
                QuickTimeEventManager.Instance.EndQTE();
            }
            catch (OperationCanceledException ex)
            {
                Debug.Log($"{LOG_TAG} RewindToBeatAsync operation cancelled: {ex.Message}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Error during rewind: {e.Message}");
                throw;
            }
            finally
            {
                delayLoop = false;
            }
        Debug.Log($"{LOG_TAG} RewindToBeatAsync END.");
    }

    private async UniTask SlowToBeatAsync()
    {
        Debug.Log($"{LOG_TAG} SlowToBeatAsync START. Token: {_destroyToken?.Token.IsCancellationRequested}");
        try
        {
            // Capture the current speed before applying slow-time effect
            if (enablePlayerSpeedControl && splineControl != null)
            {
                currentNormalSpeed = splineControl.Speed;
                Debug.Log($"{LOG_TAG} SlowToBeatAsync: Captured current normal speed: {currentNormalSpeed:F2}");
            }
            
            // Use Epoch to set the time scale for better audio integration
            Debug.Log($"{LOG_TAG} SlowToBeatAsync: Setting EpochClock '{epochClockKey}' to {slowTimeScale}.");
            SetEpochClockTimeScale(epochClockKey, slowTimeScale);
            
            // Apply player speed reduction during slow motion
            if (enablePlayerSpeedControl)
            {
                float targetSlowSpeed = currentNormalSpeed * playerSlowSpeedMultiplier;
                Debug.Log($"{LOG_TAG} SlowToBeatAsync: Applying player speed reduction from {currentNormalSpeed:F2} to {targetSlowSpeed:F2} (multiplier: {playerSlowSpeedMultiplier})");
                TransitionPlayerSpeedToValue(targetSlowSpeed, speedTransitionDuration);
            }
            
            if (audioManager != null)
            {
                Debug.Log($"{LOG_TAG} SlowToBeatAsync: Calling audioManager.SetMusicParameterAsync('Time State', 2f). Token: {_destroyToken?.Token.IsCancellationRequested}");
                await audioManager.SetMusicParameterAsync("Time State", 2f, _destroyToken.Token);
                Debug.Log($"{LOG_TAG} SlowToBeatAsync: audioManager.SetMusicParameterAsync completed.");
            }

            FluxController.Instance.ApplySlowMotionEffects();
            Debug.Log($"{LOG_TAG} SlowToBeatAsync: Starting UniTask.Delay for {slowTimeDuration}s. Token: {_destroyToken?.Token.IsCancellationRequested}");
            await UniTask.Delay(TimeSpan.FromSeconds(slowTimeDuration), cancellationToken: _destroyToken.Token);
            Debug.Log($"{LOG_TAG} SlowToBeatAsync: UniTask.Delay completed.");

            // Reset time scale and player speed
            SetEpochClockTimeScale(epochClockKey, 1f);
            
            // Restore to the captured current speed (before slow-time effect)
            if (enablePlayerSpeedControl)
            {
                Debug.Log($"{LOG_TAG} SlowToBeatAsync: Restoring player speed from {splineControl.Speed:F2} to {currentNormalSpeed:F2}");
                TransitionPlayerSpeedToValue(currentNormalSpeed, speedTransitionDuration);
            }
            
            await ResetMusicStateAsync();
            FluxController.Instance.ResetToBaseline();
        }
        catch (OperationCanceledException ex)
        {
            Debug.Log($"{LOG_TAG} SlowToBeatAsync operation cancelled: {ex.Message}");
        }
        catch (Exception e)
        {
            Debug.LogError($"{LOG_TAG} Error during slow motion: {e.Message}. Token IsCancellationRequested: {_destroyToken?.Token.IsCancellationRequested}", this.gameObject);
            throw;
        }
        Debug.Log($"{LOG_TAG} SlowToBeatAsync END.");
    }

    private void StopRewindEffect()
        {
            // Always restore to the captured current speed before the effect
            if (enablePlayerSpeedControl && splineControl != null)
            {
                splineControl.Speed = currentNormalSpeed; // Restore to captured speed immediately
                if (enableDebugLogs)
                {
                    Debug.Log($"{LOG_TAG} StopRewindEffect: Restored player speed to {currentNormalSpeed:F2} (captured before effect)");
                }
            }
            else
            {
                // Fallback to originalSpeed if player speed control is disabled
                splineControl.Speed = originalSpeed;
            }
            
            pMove.UpdateAnimation();
            splineControl.MovementDirection = MovementDirection.Forward;

            FluxController.Instance.ResetToBaseline();
            DeactivateRewindEffects();
            enemyManager.HideAllTargetIndicators();
            crosshairCore.TriggerRewindEnd();
            _ = ResetMusicStateAsync();
        }

        private void ActivateRewindEffects(bool activate)
        {
            RewindFX.enabled = activate;
            RewindFXScan.gameObject.SetActive(activate);
            temporalBlast.Play();
        }

        private void DeactivateRewindEffects()
        {
            RewindFX.enabled = false;
            RewindFXScan.gameObject.SetActive(false);
        }

        private void ApplyIncreasedDamage()
        {
            foreach (var target in playerLocking.qteEnemyLockList)
            {
                IProjectile projectile = target.GetComponent<IProjectile>();
                if (projectile != null)
                {
                    projectile.DamageAmount *= 2f; // Double the damage
                }
            }
        }

        /// <summary>
        /// Smoothly transitions the player's speed to a target multiplier
        /// </summary>
        /// <param name="targetMultiplier">Target speed multiplier (1.0 = normal speed)</param>
        /// <param name="duration">Duration of the transition</param>
        private void TransitionPlayerSpeed(float targetMultiplier, float duration)
        {
            if (!enablePlayerSpeedControl || splineControl == null)
            {
                if (enableDebugLogs && !enablePlayerSpeedControl)
                {
                    Debug.Log($"{LOG_TAG} Player speed control is disabled");
                }
                return;
            }

            // Stop any existing speed transition
            if (speedTransitionCoroutine != null)
            {
                StopCoroutine(speedTransitionCoroutine);
            }

            speedTransitionCoroutine = StartCoroutine(TransitionPlayerSpeedCoroutine(targetMultiplier, duration));
        }

        /// <summary>
        /// Smoothly transitions the player's speed to a specific target value
        /// </summary>
        /// <param name="targetSpeed">Target speed value</param>
        /// <param name="duration">Duration of the transition</param>
        private void TransitionPlayerSpeedToValue(float targetSpeed, float duration)
        {
            if (!enablePlayerSpeedControl || splineControl == null)
            {
                if (enableDebugLogs && !enablePlayerSpeedControl)
                {
                    Debug.Log($"{LOG_TAG} Player speed control is disabled");
                }
                return;
            }

            // Stop any existing speed transition
            if (speedTransitionCoroutine != null)
            {
                StopCoroutine(speedTransitionCoroutine);
            }

            speedTransitionCoroutine = StartCoroutine(TransitionPlayerSpeedToValueCoroutine(targetSpeed, duration));
        }

        /// <summary>
        /// Coroutine that handles smooth speed transitions
        /// </summary>
        private System.Collections.IEnumerator TransitionPlayerSpeedCoroutine(float targetMultiplier, float duration)
        {
            if (splineControl == null)
            {
                yield break;
            }

            float startSpeed = splineControl.Speed;
            float targetSpeed = originalPlayerSpeed * targetMultiplier;
            float elapsedTime = 0f;

            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} Transitioning player speed from {startSpeed:F2} to {targetSpeed:F2} over {duration:F2}s (multiplier: {targetMultiplier:F2})");
            }

            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime; // Use unscaled time to work during slow motion
                float t = elapsedTime / duration;
                float currentSpeed = Mathf.Lerp(startSpeed, targetSpeed, t);
                
                splineControl.Speed = currentSpeed;
                
                yield return null;
            }

            // Ensure we end up at exactly the target speed
            splineControl.Speed = targetSpeed;
            
            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} Player speed transition completed. Final speed: {splineControl.Speed:F2}");
            }

            speedTransitionCoroutine = null;
        }

        /// <summary>
        /// Coroutine that handles smooth speed transitions to a specific value
        /// </summary>
        private System.Collections.IEnumerator TransitionPlayerSpeedToValueCoroutine(float targetSpeed, float duration)
        {
            if (splineControl == null)
            {
                yield break;
            }

            float startSpeed = splineControl.Speed;
            float elapsedTime = 0f;

            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} Transitioning player speed from {startSpeed:F2} to {targetSpeed:F2} over {duration:F2}s");
            }

            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime; // Use unscaled time to work during slow motion
                float t = elapsedTime / duration;
                float currentSpeed = Mathf.Lerp(startSpeed, targetSpeed, t);
                
                splineControl.Speed = currentSpeed;
                
                yield return null;
            }

            // Ensure we end up at exactly the target speed
            splineControl.Speed = targetSpeed;
            
            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} Player speed transition to value completed. Final speed: {splineControl.Speed:F2}");
            }

            speedTransitionCoroutine = null;
        }

        /// <summary>
        /// Immediately sets the player speed to a target multiplier
        /// </summary>
        /// <param name="targetMultiplier">Target speed multiplier (1.0 = normal speed)</param>
        private void SetPlayerSpeedImmediate(float targetMultiplier)
        {
            if (!enablePlayerSpeedControl || splineControl == null)
            {
                return;
            }

            // Stop any existing speed transition
            if (speedTransitionCoroutine != null)
            {
                StopCoroutine(speedTransitionCoroutine);
                speedTransitionCoroutine = null;
            }

            float targetSpeed = originalPlayerSpeed * targetMultiplier;
            splineControl.Speed = targetSpeed;
            
            if (enableDebugLogs)
            {
                Debug.Log($"{LOG_TAG} Player speed set immediately to {targetSpeed:F2} (multiplier: {targetMultiplier:F2})");
            }
        }

        /// <summary>
        /// Refreshes the stored original player speed from the current SplineController speed
        /// Call this if the SplineController speed has been modified by other systems
        /// </summary>
        public void RefreshOriginalPlayerSpeed()
        {
            if (splineControl != null)
            {
                float previousSpeed = originalPlayerSpeed;
                originalPlayerSpeed = splineControl.Speed;
                
                if (enableDebugLogs)
                {
                    Debug.Log($"{LOG_TAG} Refreshed original player speed from {previousSpeed:F2} to {originalPlayerSpeed:F2}");
                }
            }
        }

        /// <summary>
        /// Debug method to check current speed state - call this to troubleshoot speed issues
        /// </summary>
        [ContextMenu("Debug Speed State")]
        public void DebugSpeedState()
        {
            if (splineControl != null)
            {
                Debug.Log($"{LOG_TAG} === SPEED STATE DEBUG ===");
                Debug.Log($"{LOG_TAG} Current SplineController.Speed: {splineControl.Speed:F2}");
                Debug.Log($"{LOG_TAG} Stored originalPlayerSpeed (startup): {originalPlayerSpeed:F2}");
                Debug.Log($"{LOG_TAG} Stored currentNormalSpeed (before effect): {currentNormalSpeed:F2}");
                Debug.Log($"{LOG_TAG} Stored originalSpeed (rewind legacy): {originalSpeed:F2}");
                Debug.Log($"{LOG_TAG} Player Speed Control Enabled: {enablePlayerSpeedControl}");
                Debug.Log($"{LOG_TAG} Player Slow Speed Multiplier: {playerSlowSpeedMultiplier:F2}");
                Debug.Log($"{LOG_TAG} Speed Transition Coroutine Active: {(speedTransitionCoroutine != null)}");
                Debug.Log($"{LOG_TAG} ========================");
            }
            else
            {
                Debug.LogError($"{LOG_TAG} SplineController is null - cannot debug speed state");
            }
        }

        private void SetEpochClockTimeScale(string clockKey, float timeScale)
        {
            try
            {
                // Find the EpochGlobalClock component directly (matching TimeManager approach)
                EpochGlobalClock[] globalClocks = FindObjectsByType<EpochGlobalClock>(FindObjectsSortMode.None);
                EpochGlobalClock targetClock = null;
                
                foreach (var clock in globalClocks)
                {
                    if (clock.clockKey == clockKey)
                    {
                        targetClock = clock;
                        break;
                    }
                }
                
                if (targetClock != null)
                {
                    // Set the time scale directly on the clock component
                    targetClock.LocalTimeScale = timeScale;
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"{LOG_TAG} Successfully set Epoch clock '{clockKey}' time scale to {timeScale}");
                    }
                }
                else
                {
                    Debug.LogWarning($"{LOG_TAG} Epoch clock '{clockKey}' not found");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"{LOG_TAG} Error setting Epoch clock '{clockKey}' time scale: {e.Message}");
            }
        }

        // Remove or comment out this method as it's not being used
        /*
        public void HandleRewindTime()
        {
            if (rewindTriggedStillPressed && Time.time - lastRewindTime > rewindCooldown)
            {
                lastRewindTime = Time.time;
                TriggerQTE(rewindDuration);
            }
        }
        */
    }
}
