using UnityEngine;
using BTR;
using System.Collections.Generic;
using FMOD.Studio;
using FMODUnity;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using BTR.Audio;
using Stylo.Epoch;

/// <summary>
/// Simplified AudioManager for BTR project.
/// Uses FMOD's native pooling and features instead of custom implementations.
/// Maintains Epoch time integration and backward compatibility.
/// </summary>
[DefaultExecutionOrder(-200)]
public class AudioManager : MonoBehaviour
{
    public static AudioManager Instance { get; private set; }

    [Header("FMOD Events")]
    [SerializeField]
    private EventReference musicEvent;

    [SerializeField]
    private StudioEventEmitter musicPlayback;

    [Header("Phase 2 Configuration (Optional)")]
    [SerializeField]
    [Tooltip("Optional unified audio configuration. If not assigned, uses default settings.")]
    private AudioConfigurationSO audioConfiguration;

    [Header("Audio Settings")]
    [SerializeField] private bool enableDebugLogging = false;

    // FMOD handles pooling natively - no custom pooling needed
    private bool isMusicInitialized = false;
    private readonly CancellationTokenSource _destroyToken = new();

    // Epoch time integration
    private EpochTimeline timeline;

    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudioSystem();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        // Initialize Epoch timeline integration
        timeline = FindFirstObjectByType<EpochTimeline>();
        
        // Initialize music playback asynchronously
        InitializeMusicPlaybackAsync(_destroyToken.Token).Forget();
    }

    /// <summary>
    /// Get current delta time from Epoch timeline or Unity Time.
    /// </summary>
    private float GetCurrentDeltaTime()
    {
        return timeline != null ? timeline.DeltaTime : Time.deltaTime;
    }

    private void InitializeAudioSystem()
    {
        // FMOD handles pooling natively - no custom initialization needed
        if (enableDebugLogging)
        {
            Debug.Log($"[{GetType().Name}] Audio system initialized - using FMOD native pooling");
        }
    }

    /// <summary>
    /// Initialize music playback system asynchronously.
    /// </summary>
    public async UniTask InitializeMusicPlaybackAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            // Get the StudioEventEmitter from the same GameObject
            if (musicPlayback == null)
            {
                musicPlayback = GetComponent<StudioEventEmitter>();
                if (musicPlayback == null)
                {
                    Debug.LogError($"[{GetType().Name}] StudioEventEmitter not found on the FMOD music object! Please ensure it's properly set up.");
                    return;
                }
            }

            // If musicEvent is not assigned but the StudioEventEmitter has an event reference, use that
            if (musicEvent.IsNull && !musicPlayback.EventReference.IsNull)
            {
                musicEvent = musicPlayback.EventReference;
            }

            if (!musicEvent.IsNull)
            {
                // Only set the event reference if it's different from what's already set
                if (musicPlayback.EventReference.IsNull || musicPlayback.EventReference.Guid != musicEvent.Guid)
                {
                    musicPlayback.EventReference = musicEvent;
                }

                // Check if it's already playing
                PLAYBACK_STATE playbackState;
                musicPlayback.EventInstance.getPlaybackState(out playbackState);

                if (playbackState != PLAYBACK_STATE.PLAYING)
                {
                    musicPlayback.Play();
                    // Wait for playback to start
                    await UniTask.WaitUntil(() =>
                    {
                        musicPlayback.EventInstance.getPlaybackState(out playbackState);
                        return playbackState == PLAYBACK_STATE.PLAYING;
                    }, cancellationToken: linkedCts.Token);
                }

                isMusicInitialized = true;
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] No music event assigned in AudioManager or StudioEventEmitter. Music functionality will be disabled.");
            }
        }
        catch (OperationCanceledException)
        {
            Debug.Log($"[{GetType().Name}] Music initialization cancelled");
            throw;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error initializing music: {e.Message}");
            throw;
        }
    }

    #region Sound Effects Management - Simplified

    /// <summary>
    /// Get or create FMOD EventInstance with selective time scaling control.
    /// Uses FMOD's native pooling instead of custom implementation.
    /// </summary>
    public EventInstance GetOrCreateInstance(string eventPath, bool timeScaled = true, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Default)
    {
        // Use FMOD's native instance creation - no custom pooling needed
        var instance = RuntimeManager.CreateInstance(eventPath);

        // Register with FMOD-Epoch integration for time scaling
        if (FMODEpochIntegration.Instance != null)
        {
            FMODEpochIntegration.Instance.RegisterInstance(instance, eventPath, timeScaled, category);
        }

        return instance;
    }

    /// <summary>
    /// Release FMOD EventInstance - uses FMOD's native cleanup.
    /// </summary>
    public void ReleaseInstance(string eventPath, EventInstance instance)
    {
        if (!instance.isValid()) return;

        // Unregister from Epoch integration if needed
        if (FMODEpochIntegration.Instance != null)
        {
            FMODEpochIntegration.Instance.UnregisterInstance(instance);
        }

        // Use FMOD's native instance release
        instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
        instance.release();
    }

    /// <summary>
    /// Get or create FMOD EventInstance asynchronously.
    /// Simplified version using FMOD's native features.
    /// </summary>
    public async UniTask<EventInstance> GetOrCreateInstanceAsync(string eventPath, bool timeScaled = true, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Default, CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            // Create instance on thread pool for heavy operations
            var instance = await UniTask.RunOnThreadPool(
                () => RuntimeManager.CreateInstance(eventPath),
                cancellationToken: linkedCts.Token
            );

            // Register with FMOD-Epoch integration for time scaling
            if (FMODEpochIntegration.Instance != null)
            {
                FMODEpochIntegration.Instance.RegisterInstance(instance, eventPath, timeScaled, category);
            }

            return instance;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error creating audio instance: {e.Message}");
            throw;
        }
    }

    /// <summary>
    /// Release FMOD EventInstance asynchronously.
    /// </summary>
    public async UniTask ReleaseInstanceAsync(string eventPath, EventInstance instance, CancellationToken cancellationToken = default)
    {
        if (!instance.isValid()) return;

        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            // Unregister from Epoch integration if needed
            if (FMODEpochIntegration.Instance != null)
            {
                FMODEpochIntegration.Instance.UnregisterInstance(instance);
            }

            // Release on thread pool
            await UniTask.RunOnThreadPool(() =>
            {
                instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                instance.release();
            }, cancellationToken: linkedCts.Token);
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error releasing audio instance: {e.Message}");
            throw;
        }
    }

    #region Convenience Methods

    /// <summary>
    /// Create a time-scaled FMOD instance (affected by bullet time, slow motion).
    /// </summary>
    public EventInstance GetTimeScaledInstance(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Combat)
    {
        return GetOrCreateInstance(eventPath, timeScaled: true, category: category);
    }

    /// <summary>
    /// Create a time-independent FMOD instance (always plays at normal speed).
    /// </summary>
    public EventInstance GetTimeIndependentInstance(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.UI)
    {
        return GetOrCreateInstance(eventPath, timeScaled: false, category: category);
    }

    /// <summary>
    /// Enable or disable time scaling for a specific FMOD instance at runtime.
    /// </summary>
    public bool SetInstanceTimeScaling(EventInstance instance, bool enableTimeScaling)
    {
        if (FMODEpochIntegration.Instance != null)
        {
            return FMODEpochIntegration.Instance.SetInstanceTimeScaling(instance, enableTimeScaling);
        }
        return false;
    }

    /// <summary>
    /// Get the current time scaling state of a specific FMOD instance.
    /// </summary>
    public bool GetInstanceTimeScaling(EventInstance instance)
    {
        if (FMODEpochIntegration.Instance != null)
        {
            return FMODEpochIntegration.Instance.GetInstanceTimeScaling(instance);
        }
        return false;
    }

    #endregion
    #endregion

    #region Music Management

    /// <summary>
    /// Set music parameter with enhanced error handling and validation.
    /// </summary>
    public void SetMusicParameter(string parameterName, float value)
    {
        try
        {
            if (string.IsNullOrEmpty(parameterName))
            {
                Debug.LogError($"[{GetType().Name}] Parameter name cannot be null or empty");
                return;
            }

            if (musicPlayback == null || !musicPlayback.EventInstance.isValid())
            {
                Debug.LogError($"[{GetType().Name}] Failed to set music parameter '{parameterName}': Invalid event instance");
                return;
            }

            // Configuration override support removed for simplification
            // Use FMOD Studio for parameter configuration instead

            musicPlayback.SetParameter(parameterName, value);

            if (enableDebugLogging)
            {
                Debug.Log($"[{GetType().Name}] Set music parameter '{parameterName}' to {value}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error setting music parameter '{parameterName}': {e.Message}");
        }
    }

    /// <summary>
    /// Get music parameter value.
    /// </summary>
    public float GetMusicParameter(string parameterName)
    {
        try
        {
            if (musicPlayback == null || !musicPlayback.EventInstance.isValid())
                return 0f;

            float value, finalValue;
            musicPlayback.EventInstance.getParameterByName(parameterName, out value, out finalValue);
            return finalValue;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error getting music parameter '{parameterName}': {e.Message}");
            return 0f;
        }
    }

    /// <summary>
    /// Stop music playback.
    /// </summary>
    public void StopMusic()
    {
        if (musicPlayback != null)
        {
            musicPlayback.Stop();
        }
    }

    /// <summary>
    /// Start music playback.
    /// </summary>
    public void StartMusic()
    {
        if (musicPlayback != null)
        {
            musicPlayback.Play();
        }
    }

    #endregion

    #region Backward Compatibility Methods

    /// <summary>
    /// Backward compatibility: Enhanced async instance creation.
    /// Maps to simplified GetOrCreateInstanceAsync.
    /// </summary>
    public async UniTask<EventInstance> GetOrCreateInstanceEnhancedAsync(string eventPath, bool timeScaled = true, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Default, CancellationToken cancellationToken = default)
    {
        return await GetOrCreateInstanceAsync(eventPath, timeScaled, category, cancellationToken);
    }

    /// <summary>
    /// Backward compatibility: Enhanced async instance creation with AudioConfigurationSO.AudioCategory.
    /// </summary>
    public async UniTask<EventInstance> GetOrCreateInstanceEnhancedAsync(string eventPath, AudioConfigurationSO.AudioCategory category, CancellationToken cancellationToken = default)
    {
        var fmodCategory = ConvertAudioCategory(category);
        return await GetOrCreateInstanceAsync(eventPath, true, fmodCategory, cancellationToken);
    }

    /// <summary>
    /// Backward compatibility: Async music parameter setting.
    /// Maps to synchronous SetMusicParameter.
    /// </summary>
    public async UniTask SetMusicParameterAsync(string parameterName, float value, CancellationToken cancellationToken = default)
    {
        await UniTask.RunOnThreadPool(() => SetMusicParameter(parameterName, value), cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Backward compatibility: Change music section by name.
    /// </summary>
    public void ChangeMusicSectionByName(string sectionName)
    {
        SetMusicParameter("Section", GetSectionValueFromName(sectionName));
    }

    /// <summary>
    /// Backward compatibility: Change music section by name with group.
    /// </summary>
    public void ChangeMusicSectionByName(string sectionName, int group)
    {
        SetMusicParameter("Section", GetSectionValueFromName(sectionName));
        SetMusicParameter("Group", group);
    }

    /// <summary>
    /// Backward compatibility: Change music section by name with SceneGroup.
    /// </summary>
    public void ChangeMusicSectionByName(string sectionName, SceneGroup group)
    {
        string groupName = group != null ? group.areaName : "Unknown";
        SetMusicParameter("Section", GetSectionValueFromName(sectionName));
        SetMusicParameter("GroupName", groupName.GetHashCode());
        if (enableDebugLogging)
        {
            Debug.Log($"[{GetType().Name}] ChangeMusicSectionByName - Section: {sectionName}, Group: {groupName}");
        }
    }

    /// <summary>
    /// Backward compatibility: Change song section.
    /// </summary>
    public void ChangeSongSection(float sectionValue)
    {
        SetMusicParameter("Section", sectionValue);
    }

    /// <summary>
    /// Backward compatibility: Change song section with group and scene.
    /// </summary>
    public void ChangeSongSection(SceneGroup group, int scene, float sectionValue)
    {
        // Extract meaningful identifiers from SceneGroup
        string groupName = group != null ? group.areaName : "Unknown";
        SetMusicParameter("GroupName", groupName.GetHashCode()); // Use hash for numeric parameter
        SetMusicParameter("Scene", scene);
        SetMusicParameter("Section", sectionValue);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[{GetType().Name}] ChangeSongSection - Group: {groupName}, Scene: {scene}, Section: {sectionValue}");
        }
    }

    /// <summary>
    /// Backward compatibility: Async change song section.
    /// </summary>
    public async UniTask ChangeSongSectionAsync(float sectionValue, CancellationToken cancellationToken = default)
    {
        await SetMusicParameterAsync("Section", sectionValue, cancellationToken);
    }

    /// <summary>
    /// Backward compatibility: Async change song section with group, scene, and section.
    /// </summary>
    public async UniTask ChangeSongSectionAsync(SceneGroup group, int scene, float sectionValue, CancellationToken cancellationToken = default)
    {
        string groupName = group != null ? group.areaName : "Unknown";
        await SetMusicParameterAsync("GroupName", groupName.GetHashCode(), cancellationToken);
        await SetMusicParameterAsync("Scene", scene, cancellationToken);
        await SetMusicParameterAsync("Section", sectionValue, cancellationToken);
    }

    /// <summary>
    /// Backward compatibility: Apply music changes.
    /// </summary>
    public void ApplyMusicChanges()
    {
        // No-op: Direct parameter setting doesn't require apply step
        if (enableDebugLogging)
        {
            Debug.Log($"[{GetType().Name}] ApplyMusicChanges called - no action needed with simplified system");
        }
    }

    /// <summary>
    /// Backward compatibility: Apply music changes with group, scene, and section.
    /// </summary>
    public void ApplyMusicChanges(SceneGroup group, int scene, float section)
    {
        ChangeSongSection(group, scene, section);
        ApplyMusicChanges();
    }

    /// <summary>
    /// Backward compatibility: Async apply music changes.
    /// </summary>
    public async UniTask ApplyMusicChangesAsync(CancellationToken cancellationToken = default)
    {
        await UniTask.CompletedTask;
        ApplyMusicChanges();
    }

    /// <summary>
    /// Backward compatibility: Async apply music changes with group, scene, and section.
    /// </summary>
    public async UniTask ApplyMusicChangesAsync(SceneGroup group, int scene, float section, CancellationToken cancellationToken = default)
    {
        await ChangeSongSectionAsync(group, scene, section, cancellationToken);
        await ApplyMusicChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Helper method to convert section names to values.
    /// </summary>
    private float GetSectionValueFromName(string sectionName)
    {
        return sectionName?.ToLower() switch
        {
            "intro" => 0f,
            "verse" => 1f,
            "chorus" => 2f,
            "bridge" => 3f,
            "outro" => 4f,
            _ => 0f
        };
    }

    /// <summary>
    /// Helper method to convert AudioConfigurationSO.AudioCategory to FMODEpochIntegration.AudioCategory.
    /// </summary>
    private FMODEpochIntegration.AudioCategory ConvertAudioCategory(AudioConfigurationSO.AudioCategory category)
    {
        return category switch
        {
            AudioConfigurationSO.AudioCategory.Combat => FMODEpochIntegration.AudioCategory.Combat,
            AudioConfigurationSO.AudioCategory.UI => FMODEpochIntegration.AudioCategory.UI,
            AudioConfigurationSO.AudioCategory.Music => FMODEpochIntegration.AudioCategory.Music,
            AudioConfigurationSO.AudioCategory.Environment => FMODEpochIntegration.AudioCategory.Environment,
            _ => FMODEpochIntegration.AudioCategory.Default
        };
    }

    #endregion

    #region Performance and Debug

    /// <summary>
    /// Get FMOD performance information using native FMOD features.
    /// Replaces the complex Phase 4 performance monitoring system.
    /// </summary>
    public string GetFMODPerformanceInfo()
    {
        return SimpleFMODAudioHelper.GetDebugInfo();
    }

    #endregion

    #region Cleanup

    private void OnDestroy()
    {
        _destroyToken?.Cancel();
        _destroyToken?.Dispose();

        // FMOD handles cleanup automatically - no custom pool cleanup needed
        if (enableDebugLogging)
        {
            Debug.Log($"[{GetType().Name}] AudioManager destroyed - FMOD handles cleanup automatically");
        }
    }

    #endregion
}
