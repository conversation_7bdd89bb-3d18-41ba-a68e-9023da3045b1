using System;
using System.Threading;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.VFX;
using MoreMountains.Feedbacks;
using FluffyUnderware.Curvy.Controllers;
using Cysharp.Threading.Tasks;
using Stylo.Epoch;
using BTR.Player;
using BTR.Projectiles;

namespace BTR.Player
{
    /// <summary>
    /// Handles player time control mechanics including rewind and slow motion.
    /// Refactored from PlayerTimeControl.cs to use component-based architecture.
    /// </summary>
    public class PlayerTimeControlComponent : PlayerComponent, IInputPlayerComponent, IStatefulPlayerComponent
    {
        [Header("Time Control Settings")]
        [SerializeField] private float rewindTimeScale = -2f;
        [SerializeField] private float rewindDuration = 3f;
        [SerializeField] private float slowTimeScale = 0.1f;
        [SerializeField] private float slowTimeDuration = 5f;
        [SerializeField] private float rewindCooldown = 0.5f;
        [SerializeField] private float maxRewindDuration = 1f;
        [SerializeField] private float returnToNormalDuration = 0.25f;

        [Header("Effects")]
        [SerializeField] private MMF_Player rewindFeedback;
        [SerializeField] private MMF_Player longRewindFeedback;
        [SerializeField] private ParticleSystem temporalBlast;
        [SerializeField] private ParticleSystem rewindFXScan;
        [SerializeField] private VisualEffect rewindFX;
        [SerializeField] private VisualEffect slowTimeFX;

        [Header("JPG Effect Settings")]
        [SerializeField] private float rewindJPGIntensity = 0.2f;
        [SerializeField] private float slowJPGIntensity = 0.4f;
        [SerializeField] private float jpgEffectDuration = 0.5f;

        // Component references
        private CrosshairCore crosshairCore;
        private PlayerLockingComponent playerLocking;
        private SplineController splineController;
        private PlayerMovement playerMovement;
        private AudioManager audioManager;
        private EnemyManager enemyManager;
        private ControlConfiguration controlConfig;

        // Time control state
        private float lastRewindTime;
        private float originalSpeed;
        private float baselineJPGIntensity;
        private bool isRewinding;
        private bool isSlowingTime;
        private bool delayLoop;

        // Cancellation tokens
        private readonly CancellationTokenSource destroyToken = new();
        private CancellationTokenSource currentRewindCts;

        // Input handling
        public bool ShouldReceiveInput => IsEnabled && !isRewinding;
        private bool rewindPressed;
        private bool slowTimePressed;

        #region PlayerComponent Overrides

        protected override void OnComponentInitialize()
        {
            InitializeReferences();
            LoadConfiguration();
            InitializeTimeControlState();
        }

        protected override void OnComponentStarted()
        {
            ValidateReferences();

            // Store baseline JPG intensity
            if (FluxController.Instance != null)
            {
                baselineJPGIntensity = FluxController.Instance.GetCurrentIntensity();
            }
        }

        public override void OnComponentUpdate()
        {
            HandleTimeControlInput();
        }

        protected override void OnComponentStopped()
        {
            StopAllTimeEffects();
        }

        protected override bool OnComponentValidate()
        {
            if (crosshairCore == null)
            {
                LogError("CrosshairCore component is required");
                return false;
            }

            return true;
        }

        #endregion

        #region Initialization

        private void InitializeReferences()
        {
            crosshairCore = GetComponent<CrosshairCore>();
            playerLocking = GetComponent<PlayerLockingComponent>();
            splineController = FindFirstObjectByType<SplineController>();
            playerMovement = FindFirstObjectByType<PlayerMovement>();
            audioManager = AudioManager.Instance;
            enemyManager = EnemyManager.Instance;
        }

        private void LoadConfiguration()
        {
            var playerCore = GetComponent<PlayerCore>();
            if (playerCore != null && playerCore.Configuration != null)
            {
                controlConfig = playerCore.Configuration.ControlConfig;
                ApplyConfiguration();
            }
        }

        private void ApplyConfiguration()
        {
            if (controlConfig != null)
            {
                rewindTimeScale = controlConfig.RewindTimeScale;
                rewindDuration = controlConfig.RewindDuration;
                slowTimeScale = controlConfig.SlowTimeScale;
                slowTimeDuration = controlConfig.SlowTimeDuration;
                rewindCooldown = controlConfig.RewindCooldown;
                maxRewindDuration = controlConfig.MaxRewindDuration;
                rewindJPGIntensity = controlConfig.RewindJPGIntensity;
                slowJPGIntensity = controlConfig.SlowJPGIntensity;
            }
        }

        private void ValidateReferences()
        {
            if (crosshairCore == null)
            {
                LogError("CrosshairCore not found!");
            }

            if (splineController == null)
            {
                LogWarning("SplineController not found in scene");
            }

            if (audioManager == null)
            {
                LogWarning("AudioManager not found");
            }
        }

        private void InitializeTimeControlState()
        {
            lastRewindTime = 0f;
            originalSpeed = 0f;
            isRewinding = false;
            isSlowingTime = false;
            delayLoop = false;
        }

        #endregion

        #region Input Handling

        public void HandleInput(PlayerInputData inputData)
        {
            if (!ShouldReceiveInput)
                return;

            rewindPressed = inputData.rewind;
            slowTimePressed = inputData.slowTime;
        }

        private void HandleTimeControlInput()
        {
            if (rewindPressed && CanRewind())
            {
                _ = HandleRewindToBeatAsync();
            }

            if (slowTimePressed && CanSlowTime())
            {
                _ = HandleSlowToBeatAsync();
            }
        }

        #endregion

        #region Time Control Logic

        private bool CanRewind()
        {
            return !isRewinding &&
                   !isSlowingTime &&
                   Time.time - lastRewindTime > rewindCooldown;
        }

        private bool CanSlowTime()
        {
            return !isRewinding &&
                   !isSlowingTime;
        }

        public async UniTaskVoid HandleRewindToBeatAsync()
        {
            if (!CanRewind())
                return;

            try
            {
                lastRewindTime = Time.time;

                // Check for QTE conditions
                float timeSinceLastLaunch = Time.time - crosshairCore.lastProjectileLaunchTime;
                bool hasQTETargets = playerLocking != null && playerLocking.HasLockedEnemies;

                LogDebug($"Time since last projectile launch: {timeSinceLastLaunch}, QTE Targets: {hasQTETargets}");

                if (timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW && hasQTETargets)
                {
                    LogDebug("QTE Initiated for Rewind");
                    await TriggerQTEAsync(rewindDuration, 3);
                }
                else
                {
                    LogDebug("Rewind started without QTE");
                    await RewindToBeatAsync();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("Rewind operation cancelled");
            }
            catch (Exception e)
            {
                LogError($"Error during rewind: {e.Message}");
            }
        }

        public async UniTaskVoid HandleSlowToBeatAsync()
        {
            if (!CanSlowTime())
                return;

            try
            {
                // Check for QTE conditions
                float timeSinceLastLaunch = Time.time - crosshairCore.lastProjectileLaunchTime;
                bool hasQTETargets = playerLocking != null && playerLocking.HasLockedEnemies;

                LogDebug($"Time since last projectile launch: {timeSinceLastLaunch}, QTE Targets: {hasQTETargets}");

                if (timeSinceLastLaunch <= CrosshairCore.QTE_TRIGGER_WINDOW && hasQTETargets)
                {
                    LogDebug("QTE Initiated for Slow");
                    await TriggerQTEAsync(slowTimeDuration);
                }
                else
                {
                    LogDebug("Slow started without QTE");
                    await SlowToBeatAsync();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("Slow operation cancelled");
            }
            catch (Exception e)
            {
                LogError($"Error during slow: {e.Message}");
            }
        }

        private async UniTask TriggerQTEAsync(float duration, int difficulty = 3)
        {
            if (QuickTimeEventManager.Instance == null)
            {
                LogError("QuickTimeEventManager instance is null");
                return;
            }

            try
            {
                var qteCompletionSource = new UniTaskCompletionSource<bool>();
                void HandleComplete(bool success)
                {
                    QuickTimeEventManager.Instance.OnQteComplete -= HandleComplete;
                    qteCompletionSource.TrySetResult(success);
                }

                QuickTimeEventManager.Instance.OnQteComplete += HandleComplete;
                QuickTimeEventManager.Instance.StartQTE(duration, difficulty);

                currentRewindCts?.Cancel();
                currentRewindCts?.Dispose();
                currentRewindCts = CancellationTokenSource.CreateLinkedTokenSource(destroyToken.Token);

                // Start rewind and wait for QTE completion
                _ = RewindToBeatAsync().AttachExternalCancellation(currentRewindCts.Token);
                bool success = await qteCompletionSource.Task;

                if (success)
                {
                    currentRewindCts.Cancel();
                    StopRewindEffect();
                    ApplyIncreasedDamage();
                }
                else
                {
                    playerLocking?.ClearAllLocks();
                }

                await ResetMusicStateAsync();
                LogDebug($"QTE completed with success: {success}");
            }
            catch (Exception e)
            {
                LogError($"Error during QTE: {e.Message}");
                throw;
            }
        }

        private async UniTask RewindToBeatAsync()
        {
            if (delayLoop || isRewinding)
                return;

            try
            {
                isRewinding = true;
                delayLoop = true;

                ActivateRewindEffects(true);
                enemyManager?.ShowAllTargetIndicators();

                if (splineController != null)
                {
                    float startPosition = splineController.RelativePosition;
                    originalSpeed = splineController.Speed;

                    // Calculate rewind parameters
                    float rewindSpeed = originalSpeed * 0.25f;
                    float rewindDistance = rewindSpeed * rewindDuration;
                    float newPosition = Mathf.Clamp01(startPosition - rewindDistance);

                    // Apply rewind effects
                    splineController.Speed = -rewindSpeed;

                    // Set final position after rewind
                    await UniTask.Delay(TimeSpan.FromSeconds(rewindDuration), cancellationToken: destroyToken.Token);
                    splineController.RelativePosition = newPosition;
                }

                // Apply time scale and effects
                crosshairCore?.TriggerRewindStart(rewindTimeScale);
                rewindFeedback?.PlayFeedbacks();
                FluxController.Instance?.ApplyRewindEffects();

                // Use Epoch for time control
                SetEpochClockTimeScale("Test", -1f);

                if (audioManager != null)
                {
                    await audioManager.SetMusicParameterAsync("Time State", 1f, destroyToken.Token);
                }

                await UniTask.Delay(TimeSpan.FromSeconds(rewindDuration), cancellationToken: destroyToken.Token);

                // Reset time scale
                SetEpochClockTimeScale("Test", 1f);

                StopRewindEffect();
                QuickTimeEventManager.Instance?.EndQTE();
            }
            catch (Exception e)
            {
                LogError($"Error during rewind: {e.Message}");
                throw;
            }
            finally
            {
                isRewinding = false;
                delayLoop = false;
            }
        }

        private async UniTask SlowToBeatAsync()
        {
            if (isSlowingTime)
                return;

            try
            {
                isSlowingTime = true;

                // Apply slow motion effects
                SetEpochClockTimeScale("Test", slowTimeScale);

                if (audioManager != null)
                {
                    await audioManager.SetMusicParameterAsync("Time State", 2f, destroyToken.Token);
                }

                FluxController.Instance?.ApplySlowMotionEffects();

                // Activate slow time visual effects
                if (slowTimeFX != null)
                {
                    slowTimeFX.enabled = true;
                }

                await UniTask.Delay(TimeSpan.FromSeconds(slowTimeDuration), cancellationToken: destroyToken.Token);

                // Reset time scale
                SetEpochClockTimeScale("Test", 1f);
                await ResetMusicStateAsync();
                FluxController.Instance?.ResetToBaseline();

                // Deactivate slow time effects
                if (slowTimeFX != null)
                {
                    slowTimeFX.enabled = false;
                }
            }
            catch (Exception e)
            {
                LogError($"Error during slow motion: {e.Message}");
                throw;
            }
            finally
            {
                isSlowingTime = false;
            }
        }

        #endregion

        #region Effects Management

        private void ActivateRewindEffects(bool activate)
        {
            if (rewindFX != null)
                rewindFX.enabled = activate;

            if (rewindFXScan != null)
                rewindFXScan.gameObject.SetActive(activate);

            if (temporalBlast != null)
                temporalBlast.Play();
        }

        private void DeactivateRewindEffects()
        {
            if (rewindFX != null)
                rewindFX.enabled = false;

            if (rewindFXScan != null)
                rewindFXScan.gameObject.SetActive(false);
        }

        private void StopRewindEffect()
        {
            if (splineController != null)
            {
                splineController.Speed = originalSpeed;
                splineController.MovementDirection = MovementDirection.Forward;
            }

            if (playerMovement != null)
            {
                playerMovement.UpdateAnimation();
            }

            FluxController.Instance?.SetIntensity(baselineJPGIntensity);
            DeactivateRewindEffects();
            enemyManager?.HideAllTargetIndicators();
            crosshairCore?.TriggerRewindEnd();
            _ = ResetMusicStateAsync();
        }

        private void StopAllTimeEffects()
        {
            if (isRewinding)
            {
                StopRewindEffect();
            }

            if (isSlowingTime)
            {
                SetEpochClockTimeScale("Test", 1f);
                FluxController.Instance?.ResetToBaseline();

                if (slowTimeFX != null)
                {
                    slowTimeFX.enabled = false;
                }
            }

            isRewinding = false;
            isSlowingTime = false;
        }

        #endregion

        #region Utility Methods

        private async UniTask ResetMusicStateAsync()
        {
            try
            {
                if (audioManager != null)
                {
                    LogDebug("Resetting music state");
                    await audioManager.SetMusicParameterAsync("Time State", 0f, destroyToken.Token);
                }
            }
            catch (Exception e)
            {
                LogError($"Error resetting music state: {e.Message}");
                throw;
            }
        }

        private void ApplyIncreasedDamage()
        {
            if (playerLocking == null)
                return;

            IReadOnlyList<Transform> lockedEnemies = playerLocking.GetLockedEnemies();
            foreach (var target in lockedEnemies)
            {
                if (target != null)
                {
                    var projectile = target.GetComponent<IProjectile>();
                    if (projectile != null)
                    {
                        projectile.DamageAmount *= 2f; // Double the damage
                        LogDebug($"Applied increased damage to {target.name}");
                    }
                }
            }
        }

        #endregion

        #region IStatefulPlayerComponent Implementation

        public void OnPlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            switch (newState)
            {
                case PlayerState.Dead:
                case PlayerState.Inactive:
                    StopAllTimeEffects();
                    break;

                case PlayerState.TimeControl:
                    // Already in time control state
                    break;
            }
        }

        public bool CanTransitionToState(PlayerState targetState)
        {
            switch (targetState)
            {
                case PlayerState.TimeControl:
                    return !isRewinding && !isSlowingTime;

                default:
                    return !isRewinding; // Allow most transitions except when rewinding
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Check if currently rewinding
        /// </summary>
        /// <returns>True if rewinding</returns>
        public bool IsRewinding()
        {
            return isRewinding;
        }

        /// <summary>
        /// Check if currently in slow time
        /// </summary>
        /// <returns>True if in slow time</returns>
        public bool IsSlowingTime()
        {
            return isSlowingTime;
        }

        /// <summary>
        /// Force stop all time effects
        /// </summary>
        public void ForceStopTimeEffects()
        {
            StopAllTimeEffects();
        }

        /// <summary>
        /// Set rewind cooldown
        /// </summary>
        /// <param name="cooldown">Cooldown in seconds</param>
        public void SetRewindCooldown(float cooldown)
        {
            rewindCooldown = Mathf.Max(0f, cooldown);
        }

        #endregion

        #region Epoch Clock Helper

        private void SetEpochClockTimeScale(string clockKey, float timeScale)
        {
            try
            {
                // Find the EpochGlobalClock component directly (matching TimeManager approach)
                EpochGlobalClock[] globalClocks = FindObjectsByType<EpochGlobalClock>(FindObjectsSortMode.None);
                EpochGlobalClock targetClock = null;

                foreach (var clock in globalClocks)
                {
                    if (clock.clockKey == clockKey)
                    {
                        targetClock = clock;
                        break;
                    }
                }

                if (targetClock != null)
                {
                    // Set the time scale directly on the clock component
                    targetClock.LocalTimeScale = timeScale;

                    if (enableDebugLogs)
                    {
                        LogDebug($"Successfully set Epoch clock '{clockKey}' time scale to {timeScale}");
                    }
                }
                else
                {
                    LogWarning($"Epoch clock '{clockKey}' not found");
                }
            }
            catch (System.Exception e)
            {
                LogError($"Error setting Epoch clock '{clockKey}' time scale: {e.Message}");
            }
        }

        #endregion

        #region Cleanup

        protected override void OnDestroy()
        {
            destroyToken.Cancel();
            destroyToken.Dispose();
            currentRewindCts?.Cancel();
            currentRewindCts?.Dispose();

            base.OnDestroy();
        }

        #endregion
    }
}
