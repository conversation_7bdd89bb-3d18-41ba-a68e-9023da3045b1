
using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Stylo.Flux.Universal;
using BTR;

namespace BTR
{
    /// <summary>
    /// FluxController - Manages Stylo.Flux visual effects for BTR
    /// Replaces legacy JPGEffectController with modern Flux integration
    /// </summary>
    public class FluxController : MonoBehaviour
    {
        public static FluxController Instance { get; private set; }

        [Header("🎯 Flux Integration")]
        [SerializeField] private Volume globalVolume;
        [SerializeField] private FluxEffect fluxEffect;

        [Header("⚙️ Effect Configuration")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private float baselineIntensity = 0.35f;
        [SerializeField] private float maxIntensity = 1.0f;

        [Header("🎮 Time Effect Settings")]
        [Tooltip("Flux intensity during rewind effects")]
        [SerializeField] private float rewindIntensityMultiplier = 1.5f;
        [Tooltip("Flux intensity during slow-motion effects")]
        [SerializeField] private float slowMotionIntensityMultiplier = 0.8f;

        [Header("🔧 Advanced Settings")]
        [Tooltip("Smooth transitions between intensity changes")]
        [SerializeField] private bool enableSmoothTransitions = true;
        [SerializeField] private float transitionSpeed = 2.0f;

        private float targetIntensity;
        private bool isTransitioning = false;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;
            InitializeFluxEffect();
        }

        private void Update()
        {
            if (enableSmoothTransitions && isTransitioning && fluxEffect != null)
            {
                float currentIntensity = fluxEffect.EffectIntensity.value;
                float newIntensity = Mathf.MoveTowards(currentIntensity, targetIntensity, transitionSpeed * Time.unscaledDeltaTime);

                fluxEffect.EffectIntensity.value = newIntensity;

                if (Mathf.Approximately(newIntensity, targetIntensity))
                {
                    isTransitioning = false;
                }
            }
        }

        private void InitializeFluxEffect()
        {
            // Find global volume if not assigned
            if (globalVolume == null)
            {
                globalVolume = FindObjectOfType<Volume>();
            }

            if (globalVolume == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogError($"[FluxController] No Volume found in scene. Please add a Global Volume with Flux effect.");
                }
                return;
            }

            // Get or add FluxEffect to the volume profile
            if (globalVolume.profile != null)
            {
                if (!globalVolume.profile.TryGet<FluxEffect>(out fluxEffect))
                {
                    fluxEffect = globalVolume.profile.Add<FluxEffect>();
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[FluxController] Added FluxEffect to Volume profile");
                    }
                }
            }
            else
            {
                if (enableDebugLogs)
                {
                    Debug.LogError($"[FluxController] Volume has no profile assigned");
                }
                return;
            }

            // Initialize with baseline settings
            if (fluxEffect != null)
            {
                fluxEffect.enabled.value = true;
                fluxEffect.EffectIntensity.value = baselineIntensity;
                targetIntensity = baselineIntensity;

                // Configure optimal settings for BTR gameplay
                ConfigureFluxForGameplay();

                if (enableDebugLogs)
                {
                    Debug.Log($"[FluxController] Flux effect initialized with baseline intensity: {baselineIntensity}");
                }
            }
        }

        private void ConfigureFluxForGameplay()
        {
            if (fluxEffect == null) return;

            // Configure for optimal BTR gameplay experience
            fluxEffect.ColorCrunch.value = 0.6f;
            fluxEffect.Downscaling.value = 6;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._8x8;
            fluxEffect.MotionAmplification.value = 2.5f;
            fluxEffect.TrailIntensity.value = 1.8f;
            fluxEffect.ReprojectBaseNoise.value = 0.15f;
            fluxEffect.ErrorAccumulation.value = 0.4f;

            // Enable override states
            fluxEffect.ColorCrunch.overrideState = true;
            fluxEffect.Downscaling.overrideState = true;
            fluxEffect.BlockSize.overrideState = true;
            fluxEffect.MotionAmplification.overrideState = true;
            fluxEffect.TrailIntensity.overrideState = true;
            fluxEffect.ReprojectBaseNoise.overrideState = true;
            fluxEffect.ErrorAccumulation.overrideState = true;
        }

        /// <summary>
        /// Gets the current Flux effect intensity
        /// </summary>
        public float GetCurrentIntensity()
        {
            if (fluxEffect != null)
            {
                return fluxEffect.EffectIntensity.value;
            }

            if (enableDebugLogs)
            {
                Debug.LogWarning($"[FluxController] FluxEffect not initialized, returning baseline intensity");
            }
            return baselineIntensity;
        }

        /// <summary>
        /// Sets the Flux effect intensity with optional smooth transition
        /// </summary>
        public void SetIntensity(float intensity)
        {
            if (fluxEffect == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[FluxController] FluxEffect not initialized, cannot set intensity");
                }
                return;
            }

            // Clamp intensity to valid range
            intensity = Mathf.Clamp(intensity, 0f, maxIntensity);
            targetIntensity = intensity;

            if (enableSmoothTransitions)
            {
                isTransitioning = true;
            }
            else
            {
                fluxEffect.EffectIntensity.value = intensity;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[FluxController] Setting Flux intensity to: {intensity} (smooth: {enableSmoothTransitions})");
            }
        }

        /// <summary>
        /// Applies rewind-specific visual effects
        /// </summary>
        public void ApplyRewindEffects()
        {
            if (fluxEffect == null) return;

            float rewindIntensity = baselineIntensity * rewindIntensityMultiplier;
            SetIntensity(rewindIntensity);

            // Enhance motion effects for rewind
            fluxEffect.MotionAmplification.value = 4.0f;
            fluxEffect.TrailIntensity.value = 3.0f;
            fluxEffect.ErrorAccumulation.value = 0.6f;
        }

        /// <summary>
        /// Applies slow-motion-specific visual effects
        /// </summary>
        public void ApplySlowMotionEffects()
        {
            if (fluxEffect == null) return;

            float slowIntensity = baselineIntensity * slowMotionIntensityMultiplier;
            SetIntensity(slowIntensity);

            // Reduce motion effects for slow-motion clarity
            fluxEffect.MotionAmplification.value = 1.5f;
            fluxEffect.TrailIntensity.value = 1.2f;
            fluxEffect.ErrorAccumulation.value = 0.2f;
        }

        /// <summary>
        /// Resets to baseline visual effects
        /// </summary>
        public void ResetToBaseline()
        {
            SetIntensity(baselineIntensity);
            ConfigureFluxForGameplay();
        }

        /// <summary>
        /// Context menu for testing in editor
        /// </summary>
        [ContextMenu("Test Flux Effects")]
        private void TestFluxEffects()
        {
            if (Application.isPlaying)
            {
                StartCoroutine(TestEffectsCoroutine());
            }
        }

        private System.Collections.IEnumerator TestEffectsCoroutine()
        {
            Debug.Log("[FluxController] Testing Flux effects...");

            ApplyRewindEffects();
            yield return new WaitForSeconds(2f);

            ApplySlowMotionEffects();
            yield return new WaitForSeconds(2f);

            ResetToBaseline();
            Debug.Log("[FluxController] Test complete");
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}
#else
using UnityEngine;

namespace BTR
{
    /// <summary>
    /// Fallback FluxController when URP is not installed
    /// </summary>
    public class FluxController : MonoBehaviour
    {
        public static FluxController Instance { get; private set; }

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;
            Debug.LogWarning("[FluxController] URP not installed - Flux effects disabled");
        }

        public float GetCurrentIntensity() => 0f;
        public void SetIntensity(float intensity) { }
        public void ApplyRewindEffects() { }
        public void ApplySlowMotionEffects() { }
        public void ResetToBaseline() { }
    }
}
#endif
